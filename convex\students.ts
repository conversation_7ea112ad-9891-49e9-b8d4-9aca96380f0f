import { query, mutation } from './_generated/server'
import { v } from 'convex/values'

// Get all students
export const getAll = query({
  handler: async (ctx) => {
    const students = await ctx.db.query('students').collect()
    return students
  }
})

// Get student by ID
export const getById = query({
  args: { id: v.id('students') },
  handler: async (ctx, args) => {
    const student = await ctx.db.get(args.id)
    return student
  }
})

// Get students by grade
export const getByGrade = query({
  args: { gradeLevel: v.number() },
  handler: async (ctx, args) => {
    const students = await ctx.db
      .query('students')
      .filter((q) => q.eq(q.field('gradeLevel'), args.gradeLevel))
      .collect()
    return students
  }
})

// Get students by section
export const getBySection = query({
  args: { sectionId: v.id('sections') },
  handler: async (ctx, args) => {
    const students = await ctx.db
      .query('students')
      .filter((q) => q.eq(q.field('sectionId'), args.sectionId))
      .collect()
    return students
  }
})

// Get total student count
export const getCount = query({
  handler: async (ctx) => {
    const students = await ctx.db.query('students').collect()
    return students.length
  }
})

// Get grade distribution
export const getGradeDistribution = query({
  handler: async (ctx) => {
    const students = await ctx.db.query('students').collect()

    // Initialize distribution object
    const distribution: Record<number, number> = {
      7: 0,
      8: 0,
      9: 0,
      10: 0,
      11: 0,
      12: 0
    }

    // Count students by grade
    students.forEach((student) => {
      if (student.gradeLevel in distribution) {
        distribution[student.gradeLevel]++
      }
    })

    return distribution
  }
})

// Create a new student
export const createStudent = mutation({
  args: {
    lrn: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    middleName: v.optional(v.string()),
    suffix: v.optional(v.string()),
    gender: v.union(v.literal('male'), v.literal('female')),
    gradeLevel: v.number(),
    sectionId: v.optional(v.id('sections')),
    schoolYear: v.string(),
    enrollmentStatus: v.union(
      v.literal('enrolled'),
      v.literal('pending'),
      v.literal('no longer in school'),
      v.literal('transferred')
    ),
    dropoutStatus: v.union(v.literal('dropped'), v.literal('at risk'), v.literal('none')),
    isCctRecipient: v.boolean(),
    barangay: v.string(),
    municipality: v.string(),
    province: v.string()
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString()

    // Create the student
    const studentId = await ctx.db.insert('students', {
      ...args,
      createdAt: now,
      updatedAt: now
    })

    // Update section student count if student is assigned to a section
    if (args.sectionId) {
      const section = await ctx.db.get(args.sectionId)
      if (section) {
        await ctx.db.patch(args.sectionId, {
          studentCount: (section.studentCount || 0) + 1,
          updatedAt: now
        })
      }
    }

    return studentId
  }
})

// Update an existing student
export const updateStudent = mutation({
  args: {
    id: v.id('students'),
    lrn: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    middleName: v.optional(v.string()),
    suffix: v.optional(v.string()),
    gender: v.union(v.literal('male'), v.literal('female')),
    gradeLevel: v.number(),
    sectionId: v.optional(v.id('sections')),
    schoolYear: v.string(),
    enrollmentStatus: v.union(
      v.literal('enrolled'),
      v.literal('pending'),
      v.literal('no longer in school'),
      v.literal('transferred')
    ),
    dropoutStatus: v.union(v.literal('dropped'), v.literal('at risk'), v.literal('none')),
    isCctRecipient: v.boolean(),
    barangay: v.string(),
    municipality: v.string(),
    province: v.string()
  },
  handler: async (ctx, args) => {
    const { id, ...data } = args
    const now = new Date().toISOString()

    // Get the current student data
    const student = await ctx.db.get(id)
    if (!student) {
      throw new Error('Student not found')
    }

    // Update the student
    await ctx.db.patch(id, {
      ...data,
      updatedAt: now
    })

    // If section changed, update section student counts
    if (student.sectionId !== data.sectionId) {
      // Decrement old section count
      if (student.sectionId) {
        const oldSection = await ctx.db.get(student.sectionId)
        if (oldSection) {
          await ctx.db.patch(student.sectionId, {
            studentCount: Math.max(0, (oldSection.studentCount || 0) - 1),
            updatedAt: now
          })
        }
      }

      // Increment new section count
      if (data.sectionId) {
        const newSection = await ctx.db.get(data.sectionId)
        if (newSection) {
          await ctx.db.patch(data.sectionId, {
            studentCount: (newSection.studentCount || 0) + 1,
            updatedAt: now
          })
        }
      }
    }

    return id
  }
})

// Delete a student
export const deleteStudent = mutation({
  args: { id: v.id('students') },
  handler: async (ctx, args) => {
    const now = new Date().toISOString()

    // Get the student
    const student = await ctx.db.get(args.id)
    if (!student) {
      throw new Error('Student not found')
    }

    // Delete the student
    await ctx.db.delete(args.id)

    // Update section student count if student was assigned to a section
    if (student.sectionId) {
      const section = await ctx.db.get(student.sectionId)
      if (section) {
        await ctx.db.patch(student.sectionId, {
          studentCount: Math.max(0, (section.studentCount || 0) - 1),
          updatedAt: now
        })
      }
    }

    return args.id
  }
})
