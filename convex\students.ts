import { query, mutation } from './_generated/server'
import { v } from 'convex/values'

// Get all students
export const getAll = query({
  handler: async (ctx) => {
    const students = await ctx.db.query('students').collect()
    return students
  }
})

// Get student by ID
export const getById = query({
  args: { id: v.id('students') },
  handler: async (ctx, args) => {
    const student = await ctx.db.get(args.id)
    return student
  }
})

// Get students by grade
export const getByGrade = query({
  args: { grade: v.number() },
  handler: async (ctx, args) => {
    const students = await ctx.db
      .query('students')
      .filter((q) => q.eq(q.field('grade'), args.grade))
      .collect()
    return students
  }
})

// Get students by section
export const getBySection = query({
  args: { section: v.string() },
  handler: async (ctx, args) => {
    const students = await ctx.db
      .query('students')
      .filter((q) => q.eq(q.field('section'), args.section))
      .collect()
    return students
  }
})

// Get total student count
export const getCount = query({
  handler: async (ctx) => {
    const students = await ctx.db.query('students').collect()
    return students.length
  }
})

// Get grade distribution
export const getGradeDistribution = query({
  handler: async (ctx) => {
    const students = await ctx.db.query('students').collect()

    // Initialize distribution object
    const distribution: Record<number, number> = {
      7: 0,
      8: 0,
      9: 0,
      10: 0,
      11: 0,
      12: 0
    }

    // Count students by grade
    students.forEach((student) => {
      if (student.grade in distribution) {
        distribution[student.grade]++
      }
    })

    return distribution
  }
})

// Create a new student
export const createStudent = mutation({
  args: {
    lrn: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    middleName: v.optional(v.string()),
    suffix: v.optional(v.string()),
    gender: v.union(v.literal('male'), v.literal('female')),
    gradeLevel: v.number(),
    sectionId: v.optional(v.id('sections')),
    schoolYear: v.string(),
    enrollmentStatus: v.union(
      v.literal('enrolled'),
      v.literal('pending'),
      v.literal('no longer in school'),
      v.literal('transferred')
    ),
    dropoutStatus: v.union(v.literal('dropped'), v.literal('at risk'), v.literal('none')),
    isCctRecipient: v.boolean(),
    barangay: v.string(),
    municipality: v.string(),
    province: v.string()
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString()

    // Create the student
    const studentId = await ctx.db.insert('students', {
      ...args,
      createdAt: now,
      updatedAt: now
    })

    // Update section student count
    const sections = await ctx.db
      .query('sections')
      .filter((q) => q.eq(q.field('name'), args.sectionId))
      .collect()

    if (sections.length > 0) {
      const section = sections[0]
      await ctx.db.patch(section._id, {
        studentCount: (section.studentCount || 0) + 1,
        updatedAt: now
      })
    }

    return studentId
  }
})

// Update an existing student
export const updateStudent = mutation({
  args: {
    id: v.id('students'),
    lrn: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    middleName: v.optional(v.string()),
    suffix: v.optional(v.string()),
    gender: v.union(v.literal('male'), v.literal('female')),
    gradeLevel: v.number(),
    sectionId: v.optional(v.id('sections')),
    schoolYear: v.string(),
    enrollmentStatus: v.union(
      v.literal('enrolled'),
      v.literal('pending'),
      v.literal('no longer in school'),
      v.literal('transferred')
    ),
    dropoutStatus: v.union(v.literal('dropped'), v.literal('at risk'), v.literal('none')),
    isCctRecipient: v.boolean(),
    barangay: v.string(),
    municipality: v.string(),
    province: v.string()
  },
  handler: async (ctx, args) => {
    const { id, ...data } = args
    const now = new Date().toISOString()

    // Get the current student data
    const student = await ctx.db.get(id)
    if (!student) {
      throw new Error('Student not found')
    }

    // Update the student
    await ctx.db.patch(id, {
      ...data,
      updatedAt: now
    })

    // If section changed, update section student counts
    if (student.section !== data.sectionId) {
      // Decrement old section count
      const oldSections = await ctx.db
        .query('sections')
        .filter((q) => q.eq(q.field('name'), student.section))
        .collect()

      if (oldSections.length > 0) {
        const oldSection = oldSections[0]
        await ctx.db.patch(oldSection._id, {
          studentCount: Math.max(0, (oldSection.studentCount || 0) - 1),
          updatedAt: now
        })
      }

      // Increment new section count
      const newSections = await ctx.db
        .query('sections')
        .filter((q) => q.eq(q.field('name'), data.sectionId))
        .collect()

      if (newSections.length > 0) {
        const newSection = newSections[0]
        await ctx.db.patch(newSection._id, {
          studentCount: (newSection.studentCount || 0) + 1,
          updatedAt: now
        })
      }
    }

    return id
  }
})

// Delete a student
export const deleteStudent = mutation({
  args: { id: v.id('students') },
  handler: async (ctx, args) => {
    const now = new Date().toISOString()

    // Get the student
    const student = await ctx.db.get(args.id)
    if (!student) {
      throw new Error('Student not found')
    }

    // Delete the student
    await ctx.db.delete(args.id)

    // Update section student count
    const sections = await ctx.db
      .query('sections')
      .filter((q) => q.eq(q.field('name'), student.section))
      .collect()

    if (sections.length > 0) {
      const section = sections[0]
      await ctx.db.patch(section._id, {
        studentCount: Math.max(0, (section.studentCount || 0) - 1),
        updatedAt: now
      })
    }

    return args.id
  }
})
