'use client'

import React from 'react'
import DarkModeIcon from '@mui/icons-material/DarkMode'
import LightModeIcon from '@mui/icons-material/LightMode'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import useThemeStore from '@/stores/useThemeStore'

export default function ThemeToggle({ sx = {} }: { sx?: object }) {
  const mode = useThemeStore((state) => state.mode)
  const toggleColorMode = useThemeStore((state) => state.toggleColorMode)

  return (
    <Tooltip title={mode === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}>
      <IconButton
        onClick={toggleColorMode}
        color="inherit"
        aria-label="toggle theme"
        sx={{ ml: 1, ...sx }}
      >
        {mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}
      </IconButton>
    </Tooltip>
  )
}
