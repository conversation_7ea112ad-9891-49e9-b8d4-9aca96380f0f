{"name": "nextjs-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.39.1", "@convex-dev/auth": "^0.0.84", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/material-nextjs": "^7.1.0", "@tanstack/react-form": "^1.11.1", "@tanstack/react-table": "^8.21.3", "convex": "^1.24.1", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-plugin-import": "^2.29.1", "typescript": "^5"}}