'use client'

import React, { useEffect, useState } from 'react'
import CssBaseline from '@mui/material/CssBaseline'
import { ThemeProvider as MuiThemeProvider, responsiveFontSizes } from '@mui/material/styles'
import useThemeStore, { initializeTheme } from '@/stores/useThemeStore'
import { getTheme } from '@/lib/theme'

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const mode = useThemeStore((state) => state.mode)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    initializeTheme()
    setMounted(true)
  }, [])

  const theme = getTheme(mode)

  if (!mounted) {
    return null
  }

  return (
    <MuiThemeProvider theme={responsiveFontSizes(theme)}>
      <CssBaseline />
      {children}
    </MuiThemeProvider>
  )
}
