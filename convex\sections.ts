import { query, mutation } from './_generated/server'
import { v } from 'convex/values'

// Get all sections
export const getAll = query({
  handler: async (ctx) => {
    const sections = await ctx.db.query('sections').collect()
    return sections
  }
})

// Get section by ID
export const getById = query({
  args: { id: v.id('sections') },
  handler: async (ctx, args) => {
    const section = await ctx.db.get(args.id)
    return section
  }
})

// Get sections by grade
export const getByGrade = query({
  args: { gradeLevel: v.number() },
  handler: async (ctx, args) => {
    const sections = await ctx.db
      .query('sections')
      .filter((q) => q.eq(q.field('gradeLevel'), args.gradeLevel))
      .collect()
    return sections
  }
})

// Get total section count
export const getCount = query({
  handler: async (ctx) => {
    const sections = await ctx.db.query('sections').collect()
    return sections.length
  }
})

// Get section with students
export const getSectionWithStudents = query({
  args: { id: v.id('sections') },
  handler: async (ctx, args) => {
    const section = await ctx.db.get(args.id)
    if (!section) {
      return null
    }

    const students = await ctx.db
      .query('students')
      .filter((q) => q.eq(q.field('sectionId'), args.id))
      .collect()

    return {
      ...section,
      students
    }
  }
})

// Create a new section
export const createSection = mutation({
  args: {
    name: v.string(),
    gradeLevel: v.number(),
    schoolYear: v.string(),
    adviserId: v.string(),
    maleCount: v.number(),
    femaleCount: v.number()
  },
  handler: async (ctx, args) => {
    const sectionId = await ctx.db.insert('sections', {
      ...args
    })

    return sectionId
  }
})

// Update an existing section
export const updateSection = mutation({
  args: {
    id: v.id('sections'),
    name: v.string(),
    gradeLevel: v.number(),
    schoolYear: v.string(),
    adviserId: v.string(),
    maleCount: v.number(),
    femaleCount: v.number()
  },
  handler: async (ctx, args) => {
    const { id, ...data } = args
    const now = new Date().toISOString()

    // Get the current section data
    const section = await ctx.db.get(id)
    if (!section) {
      throw new Error('Section not found')
    }

    // Update the section
    await ctx.db.patch(id, {
      ...data
    })

    // If grade changed, update student assignments
    if (section.gradeLevel !== data.gradeLevel) {
      // Get all students in this section
      const students = await ctx.db
        .query('students')
        .filter((q) => q.eq(q.field('sectionId'), id))
        .collect()

      // Update each student's section assignment based on grade compatibility
      for (const student of students) {
        // If student's grade doesn't match the new section grade, unassign from section
        if (student.gradeLevel !== data.gradeLevel) {
          await ctx.db.patch(student._id, {
            sectionId: undefined,
            updatedAt: now
          })
        }
        // If grades match, keep the student in the section (no change needed)
      }
    }

    return id
  }
})

// Delete a section
export const deleteSection = mutation({
  args: { id: v.id('sections') },
  handler: async (ctx, args) => {
    const now = new Date().toISOString()

    // Get the section
    const section = await ctx.db.get(args.id)
    if (!section) {
      throw new Error('Section not found')
    }

    // Delete the section
    await ctx.db.delete(args.id)

    // Update all students in this section to have no section
    const students = await ctx.db
      .query('students')
      .filter((q) => q.eq(q.field('sectionId'), args.id))
      .collect()

    for (const student of students) {
      await ctx.db.patch(student._id, {
        sectionId: undefined,
        updatedAt: now
      })
    }

    return args.id
  }
})

// Assign students to a section
export const assignStudents = mutation({
  args: {
    sectionId: v.id('sections'),
    studentIds: v.array(v.id('students'))
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString()

    // Get the section
    const section = await ctx.db.get(args.sectionId)
    if (!section) {
      throw new Error('Section not found')
    }

    // Get all students currently in this section
    const currentStudents = await ctx.db
      .query('students')
      .filter((q) => q.eq(q.field('sectionId'), args.sectionId))
      .collect()

    // Get the IDs of students currently in the section
    const currentStudentIds = currentStudents.map((student) => student._id)

    // Students to add (in the new list but not in the current list)
    const studentsToAdd = args.studentIds.filter((id) => !currentStudentIds.includes(id))

    // Students to remove (in the current list but not in the new list)
    const studentsToRemove = currentStudentIds.filter((id) => !args.studentIds.includes(id))

    // Add students to the section
    for (const studentId of studentsToAdd) {
      const student = await ctx.db.get(studentId)
      if (student && student.gradeLevel === section.gradeLevel) {
        await ctx.db.patch(studentId, {
          sectionId: args.sectionId,
          updatedAt: now
        })
      }
    }

    // Remove students from the section
    for (const studentId of studentsToRemove) {
      await ctx.db.patch(studentId, {
        sectionId: undefined,
        updatedAt: now
      })
    }

    // Update section student count
    await ctx.db.patch(args.sectionId, {
      studentCount: args.studentIds.length,
      updatedAt: now
    })

    // Log activity
    await ctx.db.insert('activity', {
      userId: 'system', // In a real app, this would be the current user's ID
      action: 'Updated section assignments',
      entityType: 'section',
      entityId: args.sectionId,
      details: `Updated student assignments for section ${section.name}`,
      timestamp: now
    })

    return args.sectionId
  }
})
