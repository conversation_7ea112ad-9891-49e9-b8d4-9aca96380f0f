'use client'

import Link from 'next/link'
import HomeRoundedIcon from '@mui/icons-material/HomeRounded'
import SearchOffRoundedIcon from '@mui/icons-material/SearchOffRounded'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Container from '@mui/material/Container'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'

export default function NotFound() {
  return (
    <Container
      sx={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{
          position: 'fixed',
          inset: 0,
          zIndex: -1
        }}
      >
        <Typography
          component="span"
          color="textDisabled"
          sx={{
            fontSize: { xs: '15rem', sm: '25rem', md: '40rem' },
            fontWeight: 'bold',
            opacity: 0.075
          }}
        >
          404
        </Typography>
      </Stack>

      <Container
        maxWidth="sm"
        sx={{
          position: 'relative',
          zIndex: 1
        }}
      >
        <Stack
          spacing={3}
          alignItems="center"
          textAlign="center"
        >
          <SearchOffRoundedIcon
            color="warning"
            sx={{ fontSize: { xs: '6rem', md: '8rem' } }}
          />

          <Typography
            variant="h2"
            fontWeight="bold"
          >
            I&apos;m lost!
          </Typography>

          <Typography
            variant="h6"
            color="textSecondary"
          >
            Sorry, we couldn&apos;t find the page you were looking for. It might have been moved,
            deleted, or maybe you mistyped the URL. Let&apos;s get you back on track!
          </Typography>

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
          >
            <Button
              component={Link}
              href="/"
              variant="contained"
              size="large"
              startIcon={<HomeRoundedIcon />}
            >
              Go to Homepage
            </Button>

            <Button
              component={Link}
              href="/contact"
              variant="outlined"
              size="large"
            >
              Contact Support
            </Button>
          </Stack>
        </Stack>
      </Container>
    </Container>
  )
}
