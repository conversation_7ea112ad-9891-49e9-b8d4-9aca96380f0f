'use client'

import { blue, purple, grey } from '@mui/material/colors'
import { createTheme, PaletteMode, ThemeOptions } from '@mui/material/styles'

const getThemeOptions = (mode: PaletteMode): ThemeOptions => {
  return {
    shape: {
      borderRadius: 8
    },
    typography: {
      fontFamily: 'var(--font-inter)'
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none'
          }
        }
      }
    },
    palette: {
      mode,
      ...(mode === 'light'
        ? {
            primary: {
              main: blue[700],
              light: blue[400],
              dark: blue[900]
            },
            secondary: {
              main: purple[500],
              light: purple[300],
              dark: purple[700]
            },
            background: {
              default: grey[100],
              paper: grey[50]
            },
            text: {
              primary: 'rgba(0, 0, 0, 0.87)',
              secondary: 'rgba(0, 0, 0, 0.6)',
              disabled: 'rgba(0, 0, 0, 0.38)'
            }
          }
        : {
            primary: {
              main: blue[200],
              light: blue[50],
              dark: blue[400]
            },
            secondary: {
              main: purple[200],
              light: purple[50],
              dark: purple[400]
            },
            background: {
              default: grey[900],
              paper: grey[800]
            },
            text: {
              primary: '#ffffff',
              secondary: 'rgba(255, 255, 255, 0.7)',
              disabled: 'rgba(255, 255, 255, 0.5)'
            }
          })
    }
  }
}

const lightTheme = createTheme(getThemeOptions('light'))
const darkTheme = createTheme(getThemeOptions('dark'))

export const getTheme = (mode: PaletteMode) => {
  return mode === 'dark' ? darkTheme : lightTheme
}
