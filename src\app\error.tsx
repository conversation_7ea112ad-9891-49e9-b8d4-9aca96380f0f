'use client'

import Link from 'next/link'
import HomeRoundedIcon from '@mui/icons-material/HomeRounded'
import WarningRoundedIcon from '@mui/icons-material/WarningRounded'
import Button from '@mui/material/Button'
import Container from '@mui/material/Container'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'

export default function Error({ reset }: { error: Error & { digest: string }; reset: () => void }) {
  return (
    <Container
      sx={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{
          position: 'fixed',
          inset: 0,
          zIndex: -1
        }}
      >
        <Typography
          component="span"
          color="textDisabled"
          sx={{
            fontSize: { xs: '15rem', sm: '25rem', md: '40rem' },
            fontWeight: 'bold',
            opacity: 0.075
          }}
        >
          500
        </Typography>
      </Stack>

      <Container
        maxWidth="sm"
        sx={{
          position: 'relative',
          zIndex: 1
        }}
      >
        <Stack
          spacing={3}
          alignItems="center"
          textAlign="center"
        >
          <WarningRoundedIcon
            color="error"
            sx={{ fontSize: { xs: '6rem', md: '8rem' } }}
          />

          <Typography
            variant="h2"
            fontWeight="bold"
          >
            Oh, naur!
          </Typography>

          <Typography
            variant="h6"
            color="textSecondary"
          >
            We&apos;re sorry, but an unexpected error has occurred. Please try again later or return
            to the homepage.
          </Typography>

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
          >
            <Button
              component={Link}
              href="/"
              variant="contained"
              size="large"
              startIcon={<HomeRoundedIcon />}
            >
              Go to Homepage
            </Button>

            <Button
              variant="outlined"
              size="large"
              onClick={() => reset}
            >
              Try Again
            </Button>
          </Stack>
        </Stack>
      </Container>
    </Container>
  )
}
