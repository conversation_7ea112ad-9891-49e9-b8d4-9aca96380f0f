import { defineSchema, defineTable } from 'convex/server'
import { v } from 'convex/values'
import { authTables } from '@convex-dev/auth/server'

const schema = defineSchema({
  ...authTables,

  teachers: defineTable({
    firstName: v.string(),
    lastName: v.string(),
    middleName: v.optional(v.string()),
    suffix: v.optional(v.string()),
    position: v.string()
  }),

  sections: defineTable({
    name: v.string(),
    gradeLevel: v.number(),
    schoolYear: v.string(),
    adviserId: v.string(), // Link to teachers table
    maleCount: v.number(),
    femaleCount: v.number(),
    studentCount: v.optional(v.number()),
    createdAt: v.optional(v.string()),
    updatedAt: v.optional(v.string())
  }).index('by_adviser', ['adviserId']),

  students: defineTable({
    lrn: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    middleName: v.optional(v.string()),
    suffix: v.optional(v.string()),
    gender: v.union(v.literal('male'), v.literal('female')),
    gradeLevel: v.number(),
    sectionId: v.optional(v.id('sections')), // Link to sections table
    schoolYear: v.string(),
    enrollmentStatus: v.union(
      v.literal('enrolled'),
      v.literal('pending'),
      v.literal('no longer in school'),
      v.literal('transferred')
    ),
    dropoutStatus: v.union(v.literal('dropped'), v.literal('at risk'), v.literal('none')),
    isCctRecipient: v.boolean(),
    barangay: v.string(),
    municipality: v.string(),
    province: v.string(),
    createdAt: v.optional(v.string()),
    updatedAt: v.optional(v.string())
  })
    .index('by_section', ['sectionId'])
    .index('by_lrn', ['lrn'])
    .index('by_enrollment_status', ['enrollmentStatus'])
    .index('by_dropout_status', ['dropoutStatus'])
    .index('by_grade_name', ['gradeLevel', 'lastName', 'firstName']),

  activity: defineTable({
    userId: v.string(),
    action: v.string(),
    entityType: v.string(),
    entityId: v.string(),
    details: v.string(),
    timestamp: v.string()
  })
    .index('by_user', ['userId'])
    .index('by_entity', ['entityType', 'entityId'])
    .index('by_timestamp', ['timestamp'])
})

export default schema
