'use client'

import { PaletteMode } from '@mui/material/styles'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface ThemeState {
  mode: PaletteMode
  toggleColorMode: () => void
  setMode: (mode: PaletteMode) => void
}

const useThemeStore = create<ThemeState>()(
  persist(
    (set) => ({
      mode: 'light',
      toggleColorMode: () => 
        set((state) => ({ 
          mode: state.mode === 'light' ? 'dark' : 'light' 
        })),
      setMode: (mode: PaletteMode) => set({ mode }),
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({ mode: state.mode }),
    }
  )
)

export const initializeTheme = () => {
  if (typeof window === 'undefined') return
  
  const storedTheme = localStorage.getItem('theme-storage')
  if (storedTheme) return

  const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
  useThemeStore.getState().setMode(prefersDarkMode ? 'dark' : 'light')
}

export default useThemeStore
