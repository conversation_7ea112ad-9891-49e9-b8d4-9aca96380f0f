import { v } from 'convex/values'
import { mutation, query } from './_generated/server'

// Get all teachers
export const getAll = query({
  handler: async (ctx) => {
    const teachers = await ctx.db.query('teachers').collect()
    return teachers
  }
})

// Create a new teacher
export const createTeacher = mutation({
  args: {
    id: v.id('teachers'),
    firstName: v.string(),
    lastName: v.string(),
    middleName: v.optional(v.string()),
    suffix: v.optional(v.string()),
    position: v.string()
  },
  handler: async (ctx, args) => {
    // Check if teacher already exists
    const existingTeacher = await ctx.db
      .query('teachers')
      .filter((q) => q.eq(q.field('id'), args.id))
      .first()

    if (existingTeacher) {
      throw new Error('Teacher already exists')
    }

    // Create the teacher record
    const teacherId = await ctx.db.insert('teachers', {
      ...args
    })

    return teacherId
  }
})

// Get teachers who are not assigned to any section as advisers
// export const getTeachersWithNoSection = query({
//   handler: async (ctx) => {
//     // Get all teachers
//     const teachers = await ctx.db.query('teachers').collect()

//     // Get all sections to check which teachers are already advisers
//     const sections = await ctx.db.query('sections').collect()

//     // Get the set of teacher IDs who are already advisers
//     const adviserIds = new Set(sections.map((section) => section.adviserId))

//     // Filter out teachers who are already advisers
//     const availableTeachers = teachers.filter((teacher) => !adviserIds.has(teacher._id))

//     return availableTeachers
//   }
// })
