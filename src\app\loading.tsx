'use client'

import CircularProgress from '@mui/material/CircularProgress'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'

export default function Loading() {
  return (
    <Stack
      spacing={3}
      alignItems="center"
      justifyContent="center"
      sx={{
        height: '100vh'
      }}
    >
      <CircularProgress
        variant="indeterminate"
        size={60}
        thickness={4.6}
        color="primary"
      />
      <Typography
        variant="h6"
        color="textSecondary"
        sx={{
          opacity: 0.8,
          animation: 'fadeIn 1.5s infinite alternate',
          '@keyframes fadeIn': {
            '0%': { opacity: 0.5 },
            '100%': { opacity: 0.9 }
          }
        }}
      >
        Loading...
      </Typography>
    </Stack>
  )
}
