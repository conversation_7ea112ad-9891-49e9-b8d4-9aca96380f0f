---
trigger: always_on
---

# UI Styling with Material UI
- ALWAYS use the zero-runtime CSS engine for optimal performance and React Server Components compatibility.
- PRIORITIZE using the `sx` prop for component-specific styling rather than creating custom styled components.
- When applying styles, use the theme's spacing, color, and typography values rather than hardcoded values.
- AVOID deep component nesting with Material UI components as it increases bundle size.
- ALWAYS import Material UI components directly from their dedicated paths, not from the root package.